"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";
import { motion } from "framer-motion";
import { Mail, MessageCircle, ExternalLink, Twitter as TwitterIcon, MessageSquare as DiscordIcon, Youtube as YoutubeIcon, Linkedin as LinkedinIcon, Instagram as InstagramIcon, Facebook as FacebookIcon } from "lucide-react";
import { ScrollToButton } from "@/components/common/scroll-to-button";
import { SECTION_IDS } from "@/lib/constants";
import { useAnalytics } from "@/lib/analytics/hooks";
import { ANALYTICS_EVENTS } from "@/lib/analytics/events";

const socialLinks = [
  // {
  //   name: "Twitter",
  //   url: "https://twitter.com/yourbrand",
  //   icon: TwitterIcon,
  //   color: "text-primary",
  //   bgColor: "bg-black",
  //   borderColor: "border-primary/30",
  // },
  // {
  //   name: "Discord",
  //   url: "https://discord.gg/bgCVERmA",
  //   icon: DiscordIcon,
  //   color: "text-primary",
  //   bgColor: "bg-black",
  //   borderColor: "border-primary/30",
  // },
  {
    name: "YouTube",
    url: "https://youtube.com/@tradeform?si=b9aQ5Wc-o3eQAfkz",
    icon: YoutubeIcon,
    color: "text-primary",
    bgColor: "bg-black",
    borderColor: "border-primary/30",
  },
  {
    name: "Instagram",
    url: "https://www.instagram.com/tradeform.pro?igsh=ZHB3eGdhMjQzMDg2&utm_source=qr",
    icon: InstagramIcon,
    color: "text-primary",
    bgColor: "bg-black",
    borderColor: "border-primary/30",
  },
];

export function SocialContact() {
  const { track } = useAnalytics();

  const handleEmailClick = async () => {
    // Track email interaction
    const properties = {
      contact_method: 'email',
      email_address: '<EMAIL>',
      position: 'contact_section',
      timestamp: new Date().toISOString(),
    };

    await track(ANALYTICS_EVENTS.send_email_click, properties);

    // Open email client
    window.location.href = 'mailto:<EMAIL>';
  };

  const handleContactCTAClick = async () => {
    const properties = {
      cta_text: 'Ready to Start?',
      target_section: SECTION_IDS.pricing,
      position: 'contact_section',
      timestamp: new Date().toISOString(),
    };

    await track(ANALYTICS_EVENTS.contact_cta_click, properties);
  };

  return (
    <div className="py-24 relative overflow-hidden">
      {/* Clean background */}
      <div className="absolute inset-0 z-0 bg-black">
        {/* Simple border accents */}
        <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] border border-primary/10 rounded-full opacity-20" />
        <div className="absolute bottom-1/4 right-1/4 w-[400px] h-[400px] border border-primary/20 rounded-full opacity-20" />
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-6 inline-flex items-center justify-center"
            >
              <div className="w-16 h-16 border-2 border-primary bg-black flex items-center justify-center">
                <MessageCircle className="w-8 h-8 text-primary" />
              </div>
            </motion.div>
            
            <h2 className="typography-h2 mb-4 tracking-tight">
              Stay <span className="text-primary">Connected</span>
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <p className="typography-body-l text-white/70 mb-12 max-w-2xl mx-auto">
              Questions, wins, or feedback
            </p>
          </motion.div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
            {/* Email Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-black border border-primary/30 p-6 h-full group-hover:border-primary/60 transition-all duration-300">
                <h3 className="typography-body-l font-bold text-white mb-6 text-center uppercase">
                  <span className="text-primary">Email Support</span>
                </h3>
                <p className="typography-body-s text-white/80 mb-8 text-center">
                  Get detailed answers within 24 hours
                </p>

                <div className="flex justify-center">
                  <EMAIL>
                </div>

                <div className="flex items-center justify-center gap-2 mb-6">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  <span className="typography-body-s text-xs text-white/60 uppercase tracking-wider">
                    We respond within 24 hours
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Social Media Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-black border border-primary/30 p-6 h-full group-hover:border-primary/60 transition-all duration-300">
                <h3 className="typography-body-l font-bold text-white mb-6 text-center uppercase">
                  <span className="text-primary">Social Media</span>
                </h3>
                <p className="typography-body-s text-white/80 mb-8 text-center">
                  Follow us for daily market insights, trading tips, and community events.
                </p>

                <div className="grid grid-cols-2 gap-4">
                  {socialLinks.map((social) => (
                    <motion.a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`flex flex-col items-center justify-center p-3 ${social.bgColor} ${social.borderColor} border hover:border-primary/80 transition-all duration-300`}
                      whileHover={{ y: -5, transition: { duration: 0.2 } }}
                    >
                      <social.icon className={`w-6 h-6 ${social.color} mb-2`} />
                      <span className="typography-body-s text-xs text-white/80 uppercase">{social.name}</span>
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Live Chat Section */}
            {/*<motion.div*/}
            {/*  initial={{ opacity: 0, y: 20 }}*/}
            {/*  whileInView={{ opacity: 1, y: 0 }}*/}
            {/*  transition={{ duration: 0.8, delay: 0.5 }}*/}
            {/*  viewport={{ once: true }}*/}
            {/*  className="group"*/}
            {/*>*/}
            {/*  <div className="bg-black border border-primary/30 p-6 h-full group-hover:border-primary/60 transition-all duration-300">*/}
            {/*    <h3 className="typography-body-l font-bold text-white mb-6 text-center uppercase">*/}
            {/*      <span className="text-primary">Live Chat</span>*/}
            {/*    </h3>*/}
            {/*    <p className="typography-body-s text-white/80 mb-8 text-center">*/}
            {/*      Our support team is online and ready to help with setup, strategy questions, or technical issues.*/}
            {/*    </p>*/}

            {/*    <div className="flex justify-center">*/}
            {/*      <Button*/}
            {/*        variant="default"*/}
            {/*        size="default"*/}
            {/*        className="mb-6"*/}
            {/*      >*/}
            {/*        Chat With Us*/}
            {/*      </Button>*/}
            {/*    </div>*/}

            {/*    <div className="flex items-center justify-center gap-2 mb-6">*/}
            {/*      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />*/}
            {/*      <span className="typography-body-s text-xs text-white/60 uppercase tracking-wider">*/}
            {/*        Average response: 2 minutes*/}
            {/*      </span>*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*</motion.div>*/}
          </div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-12">
              <ScrollToButton
                targetId={SECTION_IDS.pricing}
                variant="default"
                size="default"
                onClick={handleContactCTAClick}
              >
                Ready to Start? →
              </ScrollToButton>
            </div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            viewport={{ once: true }}
          >
            <div className="bg-black border border-primary/30 px-8 py-4 inline-flex flex-col sm:flex-row items-center justify-center gap-8 mt-12">
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 3, transition: { duration: 0.2 } }}
              >
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="font-mono text-sm text-white/80">Real humans, not bots</span>
              </motion.div>
              <div className="hidden sm:block w-px h-4 bg-primary/20" />
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 3, transition: { duration: 0.2 } }}
              >
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="font-mono text-sm text-white/80">24/7 community support</span>
              </motion.div>
              <div className="hidden sm:block w-px h-4 bg-primary/20" />
              <motion.div 
                className="flex items-center gap-2"
                whileHover={{ x: 3, transition: { duration: 0.2 } }}
              >
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="font-mono text-sm text-white/80">Fast response guaranteed</span>
              </motion.div>
            </div>
          </motion.div>
        </div>

      </div>
    </div>
  );
}
